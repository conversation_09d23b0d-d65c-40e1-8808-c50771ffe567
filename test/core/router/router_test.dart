import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/core/router/router.dart';
import 'package:td_procurement/core/router/routes.dart';

void main() {
  group('AppRouter Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should have kAcceptPodRoute in publicRoutes', () {
      final appRouter = container.read(routerProvider);
      expect(appRouter.publicRoutes, contains(kAcceptPodRoute.path));
    });

    test('should have correct openRoutes', () {
      final appRouter = container.read(routerProvider);
      expect(appRouter.openRoutes, contains('/'));
      expect(appRouter.openRoutes, contains(kLoginRoute.path));
      expect(appRouter.openRoutes, contains(kRegisterRoute.path));
    });

    test('publicRoutes should be separate from openRoutes', () {
      final appRouter = container.read(routerProvider);
      // Ensure publicRoutes and openRoutes don't overlap
      final intersection = appRouter.publicRoutes
          .toSet()
          .intersection(appRouter.openRoutes.toSet());
      expect(intersection, isEmpty);
    });

    test('kAcceptPodRoute should be defined correctly', () {
      expect(kAcceptPodRoute, equals('accept-pod'));
      expect(kAcceptPodRoute.path, equals('/accept-pod'));
    });

    test('should have kAcceptPodRoute as a top-level route', () {
      final appRouter = container.read(routerProvider);

      // Verify that the router has the accept pod route configured
      expect(appRouter.router.configuration.routes.any((route) {
        return route is GoRoute && route.path == kAcceptPodRoute.path;
      }), isTrue);
    });
  });
}
