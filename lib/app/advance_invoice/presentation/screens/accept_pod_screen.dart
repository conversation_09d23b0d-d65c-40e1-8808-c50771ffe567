import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/advance_invoice/data/models/retail_invoice.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/accept_pod_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class AcceptPodScreen extends ConsumerStatefulWidget {
  // final String invoiceId;
  final String token;

  const AcceptPodScreen({
    super.key,
    // required this.invoiceId,
    required this.token,
  });

  @override
  ConsumerState<AcceptPodScreen> createState() => _AcceptPodScreenState();
}

class _AcceptPodScreenState extends ConsumerState<AcceptPodScreen> {
  final ValueNotifier<bool> _isLoading = ValueNotifier(false);
  final ValueNotifier<bool> _isLoadingInvoice = ValueNotifier(false);
  RetailInvoice? _invoice;

  @override
  void initState() {
    super.initState();
    // _fetchInvoice();
  }

  // Future<void> _fetchInvoice() async {
  //   _isLoadingInvoice.value = true;

  //   try {
  //     final response =
  //         await ref.read(fetchAdvanceInvoiceUseCaseProvider(widget.invoiceId));

  //     if (mounted) {
  //       response.when(
  //         success: (invoice) {
  //           _invoice = invoice;
  //           _isLoadingInvoice.value = false;
  //         },
  //         failure: (error, code) {
  //           _isLoadingInvoice.value = false;
  //           Toast.apiError(error, context);
  //         },
  //       );
  //     }
  //   } catch (e) {
  //     if (mounted) {
  //       _isLoadingInvoice.value = false;
  //       Toast.error('Failed to load invoice details', context);
  //     }
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      body: SafeArea(
        child: ValueListenableBuilder<bool>(
          valueListenable: _isLoadingInvoice,
          builder: (context, isLoadingInvoice, _) {
            if (isLoadingInvoice) {
              return Container(
                color: const Color(0xFFF7F8FA),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      Gap(16),
                      Text(
                        'Loading invoice details...',
                        style: TextStyle(
                          color: Color(0xFF7B7B7B),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.1,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            return Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 420),
                child: SingleChildScrollView(
                  padding:
                      const EdgeInsets.symmetric(vertical: 32, horizontal: 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildHeader(textTheme),
                      const Gap(20),
                      if (_invoice != null) ...[
                        _buildInvoiceDetails(textTheme),
                        const Gap(18),
                      ],
                      _buildDescription(textTheme),
                      const Gap(28),
                      Divider(height: 1, color: Palette.stroke, thickness: 1),
                      const Gap(18),
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildInvoiceDetails(TextTheme textTheme) {
    if (_invoice == null) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Palette.stroke, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Invoice Details',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: Palette.primaryBlack,
              letterSpacing: 0.1,
            ),
          ),
          const Gap(10),
          _buildDetailRow(
              'Invoice Number', _invoice!.invoiceNumber.toString(), textTheme),
          const Gap(4),
          _buildDetailRow(
              'Amount',
              '${_invoice!.currency.symbol}${_invoice!.total.toStringAsFixed(2)}',
              textTheme),
          const Gap(4),
          _buildDetailRow(
              'Recipient', _invoice!.recipientBusinessName ?? 'N/A', textTheme),
          const Gap(4),
          _buildDetailRow(
              'Issuer', _invoice!.issuerBusinessName ?? 'N/A', textTheme),
          const Gap(4),
          _buildDetailRow(
              'Created Date',
              '${_invoice!.createdAt.day}/${_invoice!.createdAt.month}/${_invoice!.createdAt.year}',
              textTheme),
          const Gap(4),
          _buildDetailRow('Status', _invoice!.status, textTheme),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, TextTheme textTheme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.primaryBlack,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(TextTheme textTheme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Palette.primary.withValues(alpha: 0.13),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.verified_outlined,
                  color: Palette.primary,
                  size: 28,
                ),
              ),
              const Gap(12),
              Expanded(
                child: Text(
                  'Proof of Delivery Approval',
                  style: textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: Palette.primaryBlack,
                    letterSpacing: 0.1,
                  ),
                ),
              ),
            ],
          ),
          // const Gap(8),
          // Text(
          //   'Invoice ID: ${widget.invoiceId}',
          //   style: textTheme.bodyMedium?.copyWith(
          //     color: Palette.primary,
          //     fontWeight: FontWeight.w600,
          //     letterSpacing: 0.1,
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildDescription(TextTheme textTheme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 18),
      decoration: BoxDecoration(
        color: const Color(0xFFF2F6FB),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFD6E2F3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.info_outline,
                color: Color(0xFF2563EB),
                size: 22,
              ),
              const Gap(8),
              Text(
                'Important',
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: const Color(0xFF2563EB),
                  letterSpacing: 0.1,
                ),
              ),
            ],
          ),
          const Gap(10),
          Text(
            'You have received a Proof of Delivery (POD) request for this invoice. By clicking "Approve", you confirm that you have received the goods/services as described in the invoice.',
            style: textTheme.bodyMedium?.copyWith(
              color: const Color(0xFF1E293B),
              height: 1.5,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.left,
          ),
          const Gap(12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: const Color(0xFFFFF8E1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: const Color(0xFFFFECB3)),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.warning_amber_rounded,
                  color: Color(0xFFF59E42),
                  size: 18,
                ),
                const Gap(8),
                Expanded(
                  child: Text(
                    'Please ensure you have received all items before approving.',
                    style: textTheme.bodySmall?.copyWith(
                      color: const Color(0xFFB26A00),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return ValueListenableBuilder<bool>(
      valueListenable: _isLoading,
      builder: (context, isLoading, _) {
        return Column(
          children: [
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: isLoading ? null : _handleAcceptPod,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Palette.primary,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  disabledBackgroundColor: Palette.stroke,
                  textStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        letterSpacing: 0.1,
                      ),
                ),
                child: isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.check_circle_outline,
                              size: 20, color: Colors.white),
                          const Gap(8),
                          Text(
                            'Approve POD',
                            style:
                                Theme.of(context).textTheme.bodyLarge?.copyWith(
                                      fontWeight: FontWeight.w700,
                                      color: Colors.white,
                                    ),
                          ),
                        ],
                      ),
              ),
            ),
            const Gap(10),
            SizedBox(
              width: double.infinity,
              height: 48,
              child: OutlinedButton(
                onPressed: isLoading
                    ? null
                    : () => context.goNamed(kAdvanceInvoiceListRoute),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Palette.stroke, width: 1.2),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  textStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        letterSpacing: 0.1,
                      ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.close, size: 20, color: Palette.blackSecondary),
                    const Gap(8),
                    Text(
                      'Cancel',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: Palette.blackSecondary,
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _handleAcceptPod() async {
    _isLoading.value = true;

    final params = AcceptPodParams(
      // invoiceId: widget.invoiceId,
      token: widget.token,
    );

    final response = await ref.read(acceptPodUseCaseProvider(params));

    _isLoading.value = false;

    if (!mounted) return;

    response.when(
      success: (data) {
        Toast.success('POD approved successfully', context);
        context.goNamed(kAdvanceInvoiceListRoute);
      },
      failure: (error, code) {
        Toast.apiError(error, context);
      },
    );
  }

  @override
  void dispose() {
    _isLoading.dispose();
    _isLoadingInvoice.dispose();
    super.dispose();
  }
}
