import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/models/address.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_procurement/src/utils/helpers/methods.dart';

import 'index.dart';

class RetailInvoice extends Equatable {
  final String id;
  final String issuerOutletId;
  final String recipientOutletId;
  final String? note;
  final List<LineItem> items;
  final num shippingCost;
  final num processingCost;
  final num taxRate;
  final num subTotal;
  final num totalTax;
  final num? totalItemTax;
  final num total;
  final Discount? discount;
  final num? discounts;
  final bool isAdvance;
  final WalletBank bankAccount;
  final Currency currency;
  final String status;
  final String approvalStatus;
  final DateTime createdAt;
  final Address shippingAddress;
  final int invoiceNumber;
  final String? recipientBusinessName;
  final String? issuerBusinessName;

  /// Only a document is returned even though we have a list here
  final List<AdvanceInvoiceDocument> documents;
  final String? financedBy;
  final String? paymentReference;
  final num? principal;
  final String? loanId;
  final DateTime? dueDate;
  final String? documentStatus;
  final List<OtherCharge> otherCharges;
  final List<OtherCharge> otherTaxes;

  const RetailInvoice({
    required this.id,
    required this.issuerOutletId,
    required this.recipientOutletId,
    this.note,
    required this.items,
    required this.shippingCost,
    required this.processingCost,
    required this.taxRate,
    required this.subTotal,
    required this.totalTax,
    this.totalItemTax,
    required this.total,
    this.discount,
    this.discounts,
    required this.isAdvance,
    required this.bankAccount,
    required this.currency,
    required this.status,
    required this.approvalStatus,
    required this.createdAt,
    required this.shippingAddress,
    required this.invoiceNumber,
    required this.recipientBusinessName,
    required this.issuerBusinessName,
    required this.documents,
    this.financedBy,
    this.paymentReference,
    this.principal,
    this.loanId,
    this.dueDate,
    this.documentStatus,
    required this.otherCharges,
    required this.otherTaxes,
  });

  RetailInvoice copyWith({
    String? id,
    String? issuerOutletId,
    String? recipientOutletId,
    String? note,
    List<LineItem>? items,
    num? shippingCost,
    num? processingCost,
    num? taxRate,
    num? subTotal,
    num? totalTax,
    num? totalItemTax,
    num? total,
    Discount? discount,
    num? discounts,
    bool? isAdvance,
    WalletBank? bankAccount,
    Currency? currency,
    String? status,
    String? approvalStatus,
    DateTime? createdAt,
    Address? shippingAddress,
    int? invoiceNumber,
    String? recipientBusinessName,
    String? issuerBusinessName,
    List<AdvanceInvoiceDocument>? documents,
    String? financedBy,
    String? paymentReference,
    num? principal,
    String? loanId,
    DateTime? dueDate,
    String? documentStatus,
    List<OtherCharge>? otherCharges,
    List<OtherCharge>? otherTaxes,
  }) {
    return RetailInvoice(
      id: id ?? this.id,
      issuerOutletId: issuerOutletId ?? this.issuerOutletId,
      recipientOutletId: recipientOutletId ?? this.recipientOutletId,
      note: note ?? this.note,
      items: items ?? this.items,
      shippingCost: shippingCost ?? this.shippingCost,
      processingCost: processingCost ?? this.processingCost,
      taxRate: taxRate ?? this.taxRate,
      subTotal: subTotal ?? this.subTotal,
      totalTax: totalTax ?? this.totalTax,
      totalItemTax: totalItemTax ?? this.totalItemTax,
      total: total ?? this.total,
      discount: discount ?? this.discount,
      discounts: discounts ?? this.discounts,
      isAdvance: isAdvance ?? this.isAdvance,
      bankAccount: bankAccount ?? this.bankAccount,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      approvalStatus: approvalStatus ?? this.approvalStatus,
      createdAt: createdAt ?? this.createdAt,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      recipientBusinessName:
          recipientBusinessName ?? this.recipientBusinessName,
      issuerBusinessName: issuerBusinessName ?? this.issuerBusinessName,
      documents: documents ?? this.documents,
      financedBy: financedBy ?? this.financedBy,
      paymentReference: paymentReference ?? this.paymentReference,
      principal: principal ?? this.principal,
      loanId: loanId ?? this.loanId,
      dueDate: dueDate ?? this.dueDate,
      documentStatus: documentStatus ?? this.documentStatus,
      otherCharges: otherCharges ?? this.otherCharges,
      otherTaxes: otherTaxes ?? this.otherTaxes,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'issuerOutletId': issuerOutletId,
      'recipientOutletId': recipientOutletId,
      'note': note,
      'items': items.map((x) => x.toMap()).toList(),
      'shippingCost': shippingCost,
      'processingCost': processingCost,
      'taxRate': taxRate,
      'subTotal': subTotal,
      'totalTax': totalTax,
      'totalItemTax': totalItemTax,
      'total': total,
      'discount': discount?.toMap(),
      'discounts': discounts,
      'isAdvance': isAdvance,
      'bankAccount': bankAccount.toMap(),
      'currency': currency.toMap(),
      'status': status,
      'approvalStatus': approvalStatus,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'shippingAddress': shippingAddress.toMap(),
      'invoiceNumber': invoiceNumber,
      'recipientBusinessName': recipientBusinessName,
      'issuerBusinessName': issuerBusinessName,
      'documents': documents.map((x) => x.toMap()).toList(),
      'financedBy': financedBy,
      'paymentReference': paymentReference,
      'principal': principal,
      'loanId': loanId,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'documentStatus': documentStatus,
      'otherCharges': otherCharges.map((x) => x.toMap()).toList(),
      'otherTaxes': otherTaxes.map((x) => x.toMap()).toList(),
    };
  }

  factory RetailInvoice.fromMap(Map<String, dynamic> map) {
    return RetailInvoice(
      id: map['_id'] ?? map['id'] ?? '',
      issuerOutletId: map['issuerOutletId'],
      recipientOutletId: map['recipientOutletId'],
      note: map['note'],
      items: List<LineItem>.from(
        (map['items'] as List).map<LineItem>(
          (x) => LineItem.fromMap(x as Map<String, dynamic>),
        ),
      ),
      shippingCost: map['shippingCost'] ?? 0,
      processingCost: map['processingCost'] ?? 0,
      taxRate: map['taxRate'] ?? 0,
      subTotal: map['subTotal'] ?? 0,
      totalTax: map['totalTax'] ?? 0,
      totalItemTax: map['totalItemTax'],
      total: map['total'] ?? 0,
      discount: map['discount'] != null
          ? Discount.fromMap(map['discount'] as Map<String, dynamic>)
          : null,
      discounts: map['discounts'],
      isAdvance: map['isAdvance'] ?? false,
      bankAccount:
          WalletBank.fromMap(map['bankAccount'] as Map<String, dynamic>),
      currency: Currency.fromMap(map['currency'] as Map<String, dynamic>),
      status: map['status'] ?? '',
      approvalStatus: map['approvalStatus'] ?? '',
      createdAt: parseDate(map['createdAt'])!,
      shippingAddress:
          Address.fromMap(map['shippingAddress'] as Map<String, dynamic>),
      invoiceNumber: map['invoiceNumber'],
      recipientBusinessName: map['recipientBusinessName'],
      issuerBusinessName: map['issuerBusinessName'],
      documents: map['documents'] != null
          ? List<AdvanceInvoiceDocument>.from(
              (map['documents'] as List).map<AdvanceInvoiceDocument>(
                (x) =>
                    AdvanceInvoiceDocument.fromMap(x as Map<String, dynamic>),
              ),
            )
          : [],
      financedBy: map['financedBy'],
      paymentReference: map['paymentReference'],
      principal: map['principal'],
      loanId: map['loanId'],
      dueDate: parseDate(map['dueDate']),
      documentStatus: map['documentStatus'],
      otherCharges: map['otherCharges'] != null
          ? List<OtherCharge>.from(
              (map['otherCharges'] as List).map<OtherCharge>(
                (x) => OtherCharge.fromMap(x as Map<String, dynamic>),
              ),
            )
          : [],
      otherTaxes: map['otherTaxes'] != null
          ? List<OtherCharge>.from(
              (map['otherTaxes'] as List).map<OtherCharge>(
                (x) => OtherCharge.fromMap(x as Map<String, dynamic>),
              ),
            )
          : [],
    );
  }

  @override
  List<Object?> get props {
    return [
      id,
      issuerOutletId,
      recipientOutletId,
      note,
      items,
      shippingCost,
      processingCost,
      taxRate,
      subTotal,
      totalTax,
      totalItemTax,
      total,
      discount,
      discounts,
      isAdvance,
      bankAccount,
      currency,
      status,
      approvalStatus,
      createdAt,
      shippingAddress,
      invoiceNumber,
      recipientBusinessName,
      issuerBusinessName,
      documents,
      financedBy,
      paymentReference,
      principal,
      loanId,
      dueDate,
      documentStatus,
      otherCharges,
      otherTaxes,
    ];
  }

  String toJson() => json.encode(toMap());

  factory RetailInvoice.fromJson(String source) =>
      RetailInvoice.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  bool get showPaymentDetail =>
      isAdvance &&
      isApproved &&
      bankAccount.bankName != null &&
      bankAccount.accountNumber != null;

  bool get isApproved => approvalStatus == 'approved' || status == 'accepted';

  bool get requirePodUpload =>
      approvalStatus.toLowerCase() == 'pending' &&
      (documentStatus?.toLowerCase() == 'rejected' ||
          documentStatus?.toLowerCase() == 'pending');

  // bool get requirePodUpload =>
  //     approvalStatus.toLowerCase() == 'pending' &&
  //     (documents.isEmpty
  //         ? true
  //         : documents.first.status.isEmpty
  //             ? true
  //             : documents.first.status.toLowerCase() == 'rejected');

  // bool get canUploadPod =>
  //     documents.isEmpty || documents.first.status == 'rejected';

  factory RetailInvoice.defaultValue() {
    return RetailInvoice(
      id: 'id',
      issuerOutletId: 'issuerOutletId',
      recipientOutletId: 'recipientOutletId',
      note: 'note',
      items: List.filled(5, LineItem.defaultValue()),
      shippingCost: 0,
      processingCost: 0,
      taxRate: 0,
      subTotal: 0,
      totalTax: 0,
      totalItemTax: 0,
      total: 0,
      discount: null,
      discounts: 0,
      isAdvance: false,
      bankAccount: WalletBank.defaultValue(),
      currency: Currency(iso: 'NGN', symbol: '₦'),
      status: 'status',
      approvalStatus: 'approvalStatus',
      createdAt: DateTime.now(),
      shippingAddress: Address(
        id: 'id',
        company: 'company',
        isBillingDefault: false,
        isShippingDefault: false,
        lga: 'lga',
        state: 'state',
        phone: 'phone',
        address1: 'address1',
        fullName: 'fullName',
        country: 'country',
      ),
      invoiceNumber: 0123456,
      recipientBusinessName: 'recipientBusinessName',
      issuerBusinessName: 'issuerBusinessName',
      documents: List.filled(5, AdvanceInvoiceDocument.defaultValue()),
      financedBy: 'financedBy',
      paymentReference: 'paymentReference',
      principal: 0,
      loanId: 'loanId',
      dueDate: DateTime.now(),
      documentStatus: 'status',
      otherCharges: List.filled(5, const OtherCharge(name: 'name', amount: 0)),
      otherTaxes: List.filled(5, const OtherCharge(name: 'name', amount: 0)),
    );
  }
}

class AdvanceInvoiceDocument extends Equatable {
  final String url;
  final String status;
  final String message;
  final DateTime? createdAt;
  final DateTime? uploadedAt;
  final DateTime? updatedAt;
  final String retailInvoiceId;
  final String documentType;

  const AdvanceInvoiceDocument({
    required this.url,
    required this.status,
    required this.message,
    this.createdAt,
    this.uploadedAt,
    this.updatedAt,
    required this.retailInvoiceId,
    required this.documentType,
  });

  factory AdvanceInvoiceDocument.fromMap(Map<String, dynamic> map) {
    return AdvanceInvoiceDocument(
      url: map['url'],
      status: map['status'] ?? '',
      message: map['message'] ?? '',
      createdAt: parseDate(map['createdAt']),
      uploadedAt: parseDate(map['uploadedAt']),
      updatedAt: parseDate(map['updatedAt']),
      retailInvoiceId: map['retailInvoiceId'] ?? '',
      documentType: map['documentType'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'url': url,
      'status': status,
      'message': message,
      'createdAt': createdAt?.millisecondsSinceEpoch,
      'uploadedAt': uploadedAt?.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'retailInvoiceId': retailInvoiceId,
      'documentType': documentType,
    };
  }

  @override
  String toString() => '${toMap()}';

  @override
  bool get stringify => true;

  @override
  List<Object?> get props => [
        url,
        documentType,
        status,
        message,
        createdAt,
        uploadedAt,
        updatedAt,
        retailInvoiceId,
      ];

  factory AdvanceInvoiceDocument.defaultValue() {
    return AdvanceInvoiceDocument(
      url: 'url',
      status: 'status',
      message: 'message',
      createdAt: DateTime.now(),
      uploadedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      retailInvoiceId: 'retailInvoiceId',
      documentType: 'documentType',
    );
  }
}

class OtherCharge extends Equatable {
  final String name;
  final num amount;

  const OtherCharge({
    required this.name,
    required this.amount,
  });

  OtherCharge copyWith({
    String? name,
    num? amount,
  }) {
    return OtherCharge(
      name: name ?? this.name,
      amount: amount ?? this.amount,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'name': name,
      'amount': amount,
    };
  }

  factory OtherCharge.fromMap(Map<String, dynamic> map) {
    return OtherCharge(
      name: map['name'] as String,
      amount: parseNum(map['amount']) ?? 0,
    );
  }

  @override
  List<Object?> get props => [name, amount];
}
