import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_flutter_core/services/api/td_api.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/accept_pod_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/add_item_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/advance_invoice_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/create_retail_invoice_param.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/email_pod_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/search_params.dart';
import 'package:td_procurement/app/advance_invoice/domain/entities/upload_pod_params.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/config/app_config/app_config.dart';
import 'package:td_procurement/core/models/query_parameters.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';

final advanceInvoiceDataSourceProvider =
    Provider.autoDispose<AdvanceInvoiceDataSource>((ref) {
  final apiClient = ref.read(apiClientProvider);
  final config = ref.read(appConfigProvider);
  return AdvanceInvoiceDataSourceImplementation(apiClient, config);
});

abstract class AdvanceInvoiceDataSource {
  Future<AdvanceInvoiceSettings> fetchAdvanceInvoiceSettings();
  Future<void> createAdditionalCharge(AdditionalCharge params);
  Future<void> createTaxRate(TaxRate params);
  Future<RetailInvoice> createRetailInvoice(CreateRetailInvoiceParam params);
  Future<RetailInvoice> fetchAdvanceInvoice(String id);
  Future<void> deleteAdvanceInvoice(String id);
  Future<FetchAdvanceInvoicesResponse> fetchAdvanceInvoices(
      FetchAdvanceInvoiceParams params);
  Future<void> uploadAdvanceInvoiceDoc(String id, String doc);
  Future<void> uploadPod(UploadPodParams params);
  Future<void> emailPod(EmailPodParams params);
  Future<void> acceptPod(AcceptPodParams params);
  Future<void> uploadLogo(String imageInBase64);
  Future<LoanContract> getLoanContract();
  Future<List<RetailOutlet>> searchMerchant(String searchTerm);
  Future<List<WalletBank>> getBankAccounts();
  Future<List<SettlementBank>> getSettlementBanks();
  Future<(String bankName, String? bvn)> validateBankAccount(
      String accountNumber, String bankCode);
  Future<bool> linkBankAccount(LinkBankParams params);
  Future<List<Variant>> searchRetailProducts(SearchParams params);
  Future<List<Order>> searchOrders(SearchOrderParams params);
  Future<void> addItem(AddItemParams params);
}

class AdvanceInvoiceDataSourceImplementation
    implements AdvanceInvoiceDataSource {
  final TdApiClient _apiClient;
  final AppConfig _config;

  AdvanceInvoiceDataSourceImplementation(this._apiClient, this._config);

  @override
  Future<AdvanceInvoiceSettings> fetchAdvanceInvoiceSettings() async {
    final res = await _apiClient
        .get('${_config.consoleUrl}$kAdvanceInvoiceSettingsApiPath');
    return AdvanceInvoiceSettings.fromMap(res.data);
  }

  @override
  Future<void> createAdditionalCharge(AdditionalCharge params) async {
    await _apiClient.post(kCreateAdditionalChargeApiPath,
        data: params.toData());
    return;
  }

  @override
  Future<void> createTaxRate(TaxRate params) async {
    await _apiClient.post('${_config.consoleUrl}$kCreateTaxRateApiPath',
        data: params.toData());
    return;
  }

  @override
  Future<RetailInvoice> createRetailInvoice(
      CreateRetailInvoiceParam params) async {
    final res = await _apiClient.post(
        '${_config.consoleUrl}$kCreateRetailInvoiceApiPath',
        data: params.toData());
    return RetailInvoice.fromMap(res.data['data']);
  }

  @override
  Future<void> deleteAdvanceInvoice(String id) async {
    await _apiClient.delete(
        '${_config.consoleUrl}${kAdvanceInvoiceItemApiPath.replaceFirst(':id', id)}');
    return;
  }

  @override
  Future<RetailInvoice> fetchAdvanceInvoice(String id) async {
    final res = await _apiClient.get(
        '${_config.consoleUrl}${kAdvanceInvoiceItemApiPath.replaceFirst(':id', id)}');
    return RetailInvoice.fromMap(res.data);
  }

  @override
  Future<FetchAdvanceInvoicesResponse> fetchAdvanceInvoices(
      FetchAdvanceInvoiceParams params) async {
    final queryString = getQueryString(params);

    final res = await _apiClient.get(
        '${_config.consoleUrl}${kAdvanceInvoiceListApiPath.replaceFirst(':queryString', queryString)}');

    final data = res.data['data'];
    final List<dynamic> retailInvoices = data['retailInvoices'];
    final Map<String, dynamic> pagination = data['pagination'];

    return FetchAdvanceInvoicesResponse(
      retailInvoices:
          retailInvoices.map((x) => RetailInvoice.fromMap(x)).toList(),
      queryParams: QueryParameters.fromMap(pagination),
    );
  }

  // TODO: confirm doc upload format (form data or json)
  @override
  Future<void> uploadAdvanceInvoiceDoc(String id, String doc) async {
    await _apiClient.post(
        '${_config.consoleUrl}${kAdvanceInvoiceDocUploadApiPath.replaceFirst(':id', id)}',
        data: {
          'doc': doc,
        });
    return;
  }

  @override
  Future<void> uploadPod(UploadPodParams params) async {
    await _apiClient.post(
        '${_config.consoleUrl}${kAdvanceInvoiceDocUploadApiPath.replaceFirst(':id', params.invoiceId)}',
        data: params.toMap());
    return;
  }

  @override
  Future<void> emailPod(EmailPodParams params) async {
    await _apiClient.post(
        '${_config.consoleUrl}${kEmailPodApiPath.replaceFirst(':id', params.invoiceId)}');
    return;
  }

  @override
  Future<void> acceptPod(AcceptPodParams params) async {
    await _apiClient.post(
        '${_config.consoleUrl}${kAcceptPodApiPath.replaceFirst(':token', params.token)}');
    return;
  }

  @override
  Future<void> uploadLogo(String imageInBase64) async {
    await _apiClient.post('${_config.consoleUrl}$kLogoUploadApiPath', data: {
      'image': imageInBase64,
    });
    return;
  }

  @override
  Future<LoanContract> getLoanContract() async {
    final res = await _apiClient.get('${_config.awsApiUrlV4}/loancontracts');
    return LoanContract.fromMap(res.data);
  }

  @override
  Future<List<RetailOutlet>> searchMerchant(String searchTerm) async {
    //TODO: add the url when provided from the backend
    final res = await _apiClient.post('', data: {
      'searchTerm': searchTerm,
    });
    final List<dynamic> list = res.data['body'];
    return list.map((x) => RetailOutlet.fromMap(x)).toList();
  }

  @override
  Future<List<WalletBank>> getBankAccounts() async {
    try {
      final res = await _apiClient.get('${_config.awsApiUrlV2}/list-accounts');

      if (res.data == null ||
          res.data["data"] == null ||
          res.data["data"] is! List) {
        return [];
      }

      final icons = await getMonoIcons();
      final List<dynamic> bankData = res.data["data"];

      return bankData.map((bankInfo) {
        final bankName = bankInfo["bankName"] as String?;
        final matchingIcon = icons.firstWhere(
          (element) =>
              element.bankName?.toLowerCase() == bankName?.toLowerCase(),
          orElse: () => (bankName: null, icon: null),
        );

        return WalletBank.fromMap(bankInfo, icon: matchingIcon.icon);
      }).toList();
    } catch (e) {
      // Log error if needed
      return [];
    }
  }

  @override
  Future<List<SettlementBank>> getSettlementBanks() async {
    final res = await _apiClient.get('${_config.awsApiUrlV2}/list-banks');

    if (res.data == null ||
        res.data["data"] == null ||
        res.data["data"] is! List) {
      return [];
    }

    final List<dynamic> bankData = res.data["data"];
    return bankData.map((e) => SettlementBank.fromMap(e)).toList();
  }

  @override
  Future<(String bankName, String? bvn)> validateBankAccount(
      String accountNumber, String bankCode) async {
    final res = await _apiClient.get('${_config.awsApiUrlV2}/get-account-name',
        queryParameters: {
          "accountNumber": accountNumber,
          "bankCode": bankCode
        });

    if (res.data == null ||
        res.data["data"] == null ||
        res.data["data"] is! Map) {
      return ('', null);
    }

    final data = (res.data["data"] as Map);
    return (data["accountName"] as String, data["accountBVN"] as String?);
  }

  @override
  Future<bool> linkBankAccount(LinkBankParams params) async {
    await _apiClient.post('${_config.awsApiUrlV2}/link-bank-account',
        data: params.toMap());
    return true;
  }

  @override
  Future<List<Variant>> searchRetailProducts(SearchParams params) async {
    final res = await _apiClient.post('${_config.searchUrl}/search-invoices',
        data: params.toMap());
    final List<dynamic> list = res.data['body'];
    return list.map((x) => Variant.fromMap(x)).toList();
  }

  @override
  Future<List<Order>> searchOrders(SearchOrderParams params) async {
    final res = await _apiClient.post('${_config.searchUrl}/search',
        data: params.toMap());
    final List<dynamic> list = res.data['body'];
    return list.map((x) => Order.fromMap(x)).toList();
  }

  @override
  Future<void> addItem(AddItemParams params) async {
    await _apiClient.post('${_config.consoleUrl}/api/v3/invoice/create-product',
        data: params.toMap());
    return;
  }

  Future<List<({String? bankName, String? icon})>> getMonoIcons() async {
    try {
      final result =
          await _apiClient.get("https://api.withmono.com/v1/institutions");
      return (result.data as List<dynamic>)
          .map(
            (e) => (bankName: e["name"] as String?, icon: e["icon"] as String?),
          )
          .toList();
    } catch (_) {
      return [];
    }
  }
}
