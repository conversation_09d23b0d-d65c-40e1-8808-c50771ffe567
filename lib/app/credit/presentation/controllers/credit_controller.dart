import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/credit/data/models/credit_statement.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';
import 'package:td_procurement/app/credit/domain/use_cases/credit_use_cases.dart';

class CreditTableController extends AutoDisposeFamilyAsyncNotifier<
    CreditStatement?, CreditStatementParams?> {
  @override
  FutureOr<CreditStatement?> build(arg) async {
    return arg != null
        ? (await ref.read(
            creditUseCaseProvider(arg),
          ))
            .extract()
        : null;
  }
}

final creditTableControllerProvider = AutoDisposeAsyncNotifierProviderFamily<
    CreditTableController, CreditStatement?, CreditStatementParams?>(() {
  return CreditTableController();
});

final creditStatementArgProvider = StateProvider<CreditStatementParams?>((_) => null);

final creditStatementControllerProvider = AutoDisposeFutureProvider((ref) async {
  ref.invalidate(creditStatementArgProvider);
  return (await ref.read(
    creditUseCaseProvider(
      CreditStatementParams(),
    ),
  ))
      .extract();
});
