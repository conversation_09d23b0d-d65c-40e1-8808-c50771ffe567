import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:number_pagination/number_pagination.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/account/data/models/account_statement.dart';
import 'package:td_procurement/app/credit/data/models/loan_repayment.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';
import 'package:td_procurement/app/credit/presentation/controllers/credit_controller.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/components/widgets/empty_widget.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class CreditTableWidget extends ConsumerStatefulWidget {
  final List<Loan> loans;
  final Currency currency;
  final int? total;
  const CreditTableWidget(this.loans, this.currency, {super.key, this.total});

  @override
  ConsumerState<CreditTableWidget> createState() => _CreditTableWidgetState();
}

class _CreditTableWidgetState extends ConsumerState<CreditTableWidget> {
  @override
  Widget build(BuildContext context) {
    final params = ref.watch(creditStatementArgProvider);
    final transactionsState = ref.watch(creditTableControllerProvider(params));

    return transactionsState.when(
      data: (data) {
        final allRepayments = data?.allRepayments ??
            widget.loans.expand((loan) => loan.repayments).toList();

        if (allRepayments.isEmpty) {
          return const EmptyWidget(
            icon: kReceiptSvg,
            title: 'No credit statements',
            subTitle:
                'There are no credit statements matching your filter criteria',
          );
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: _TableContent(
                  repayments: allRepayments,
                  currency: widget.currency,
                ),
              ),
            ),
            const Gap(20),
            CreditPagination(total: widget.total),
            const Gap(20),
          ],
        );
      },
      error: (error, __) => FailureWidget(
        e: error,
        retry: () => ref.invalidate(creditTableControllerProvider),
      ),
      loading: () => const CreditTableLoadingView(),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }
}

class CreditTableLoadingView extends StatelessWidget {
  const CreditTableLoadingView({super.key});

  @override
  Widget build(BuildContext context) {
    final defaultLoadRepayment = LoanRepayment(
        "repaymentId",
        "retailOutletId",
        "loanId",
        1000,
        "pending",
        DateTime.now(),
        DateTime.now(),
        1000,
        0,
        100,
        900,
        0,
        DateTime.now());
    return Skeletonizer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: _TableContent(
                repayments: List.filled(20, defaultLoadRepayment),
              ),
            ),
          ),
          const Gap(20),
          const CreditPagination(),
          const Gap(20),
        ],
      ),
    );
  }
}

class _TableContent extends StatelessWidget {
  final List<LoanRepayment> repayments;
  final Currency? currency;

  const _TableContent({
    required this.repayments,
    this.currency,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Palette.kE7E7E7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DataTable(
        showCheckboxColumn: false,
        headingRowColor: WidgetStatePropertyAll(Palette.kF7F7F7),
        columns: [
          DataColumn(
            label: _buildHeaderCell('Due Date', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Loan ID', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Amount', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Principal', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Interest', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Status', textTheme),
          ),
          const DataColumn(
            label: SizedBox.shrink(),
          ), // Empty space for floating icon
        ],
        rows: repayments
            .map(
              (element) => DataRow(
                onSelectChanged: (_) {},
                cells: [
                  DataCell(
                    _buildContentText(element.dueAt.toDate(), textTheme),
                  ),
                  DataCell(
                    _buildContentText(element.loanId, textTheme),
                  ),
                  DataCell(
                    _buildContentAmount(
                        element.amount, textTheme, Palette.k0CA653, currency),
                  ),
                  DataCell(
                    _buildContentAmount(element.principal, textTheme,
                        Palette.primaryBlack, currency),
                  ),
                  DataCell(
                    _buildContentAmount(
                        element.interest, textTheme, Palette.kE61010, currency),
                  ),
                  DataCell(
                    _buildStatusChip(element.status),
                  ),
                  const DataCell(
                    SizedBox.shrink(),
                  )
                ],
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildContentAmount(num amount, TextTheme textTheme,
      [Color? color, Currency? currency]) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: CurrencyWidget(
        amount,
        currency?.iso ?? kDefaultCurrency,
        amountStyle: textTheme.bodyMedium?.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildContentText(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(
          color: Palette.blackSecondary,
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    Color textColor;

    switch (status.toLowerCase()) {
      case 'pending':
        backgroundColor = Colors.orange.withValues(alpha: 0.1);
        textColor = Colors.orange;
        break;
      case 'paid':
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        textColor = Colors.green;
        break;
      case 'overdue':
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        textColor = Colors.red;
        break;
      default:
        backgroundColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.toUpperCase(),
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

class CreditPagination extends ConsumerWidget {
  final int? total;
  const CreditPagination({super.key, this.total});

  @override
  Widget build(BuildContext context, ref) {
    final params =
        ref.watch(creditStatementArgProvider) ?? CreditStatementParams();
    final int batches = ((ref
                    .read(creditTableControllerProvider(params))
                    .valueOrNull
                    ?.totalCount ??
                total ??
                0) /
            params.limit)
        .ceil();
    return Align(
      alignment: Alignment.topRight,
      child: SizedBox(
        width: 500,
        child: NumberPagination(
          onPageChanged: (int pageNumber) {
            ref.read(creditStatementArgProvider.notifier).state =
                params.paginate(pageNumber);
          },
          visiblePagesCount: batches > 5 || batches == 0 ? 5 : batches,
          buttonElevation: 0.3,
          totalPages: batches,
          currentPage: params.batch,
          buttonRadius: 8,
          selectedButtonColor: Palette.primaryBlack,
          selectedNumberColor: Colors.white,
          unSelectedButtonColor: Colors.white,
          unSelectedNumberColor: Palette.blackSecondary,
          fontSize: 14,
          firstPageIcon: SvgPicture.asset(
            kDoubleChevronLeftSvg,
            width: 30,
            height: 30,
          ),
          previousPageIcon: SvgPicture.asset(
            kChevronLeftSvg,
            colorFilter:
                ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
            width: 12,
            height: 12,
          ),
          lastPageIcon: SvgPicture.asset(
            kDoubleChevronRightSvg,
            width: 30,
            height: 30,
          ),
          nextPageIcon: SvgPicture.asset(
            kChevronRightSvg,
            colorFilter:
                ColorFilter.mode(Palette.blackSecondary, BlendMode.srcIn),
            width: 12,
            height: 12,
          ),
        ),
      ),
    );
  }
}
