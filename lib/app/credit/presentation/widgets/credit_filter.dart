import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';
import 'package:td_procurement/app/credit/presentation/controllers/credit_controller.dart';
import 'package:td_procurement/app/shipments/presentation/widgets/date_picker.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class CreditFilter extends ConsumerStatefulWidget {
  const CreditFilter({super.key});

  @override
  ConsumerState<CreditFilter> createState() => _CreditFilterState();
}

class _CreditFilterState extends ConsumerState<CreditFilter> {
  final ValueNotifier<List<DateTime>> _selectedDates =
      ValueNotifier<List<DateTime>>([]);
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _selectedDates.dispose();
    _closeOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // final textTheme = Theme.of(context).textTheme;
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Palette.kE7E7E7),
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
              color: Palette.k0000000A,
              offset: Offset(0, 4),
              blurRadius: 12,
              spreadRadius: 0)
        ],
      ),
      height: 34,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          DropdownMenu<CreditStatementSorting>(
            trailingIcon: SvgPicture.asset(
              kChevronDownSvg,
              fit: BoxFit.cover,
            ),
            hintText: 'Sort by',
            width: 145,
            enableSearch: false,
            requestFocusOnTap: false,
            selectedTrailingIcon: SvgPicture.asset(
              kChevronDownSvg,
              fit: BoxFit.cover,
            ),
            inputDecorationTheme: InputDecorationTheme(
              hintStyle: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                  color: Palette.primaryBlack),
              enabledBorder: OutlineInputBorder(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  bottomLeft: Radius.circular(8),
                ),
                borderSide: BorderSide(color: Palette.kE7E7E7, width: 0.1),
              ),
              contentPadding: const EdgeInsets.only(left: 10),
              constraints: BoxConstraints.tight(
                const Size.fromHeight(34),
              ),
            ),
            onSelected: sortTable,
            dropdownMenuEntries: CreditStatementSorting.values
                .map<DropdownMenuEntry<CreditStatementSorting>>(
                  (option) => DropdownMenuEntry<CreditStatementSorting>(
                    value: option,
                    label: option.label,
                    style: MenuItemButton.styleFrom(
                        foregroundColor: Palette.primaryBlack),
                  ),
                )
                .toList(),
          ),
          VerticalDivider(
            color: Palette.kE7E7E7,
            thickness: 1.5,
            width: 20,
          ),
          ValueListenableBuilder<List<DateTime>>(
            valueListenable: _selectedDates,
            builder: (context, selectedDates, _) {
              return DatePickerWidget(
                initialDate: selectedDates.firstOrNull,
                selectedStartDate: selectedDates.firstOrNull,
                selectedEndDate: selectedDates.lastOrNull,
                offset: const Offset(455, 40),
                decoration: const BoxDecoration(),
                getValueLabel: () {
                  return getFormattedDateRange(selectedDates.firstOrNull,
                      selectedDates.lastOrNull, 'Select Date');
                },
                onDatesSelected: ({startDate, endDate}) async {
                  final selectedStartDate = startDate ??= endDate;
                  final selectedEndDate = endDate ??= startDate;
                  final params = ref.read(creditStatementArgProvider);
                  ref.read(creditStatementArgProvider.notifier).state =
                      CreditStatementParams(
                          startDate: selectedStartDate,
                          endDate: selectedEndDate,
                          paymentType: params?.paymentType);
                  _selectedDates.value = [selectedStartDate!, selectedEndDate!];
                },
                onCancel: () async {
                  final params = ref.read(creditStatementArgProvider);
                  ref.read(creditStatementArgProvider.notifier).state =
                      CreditStatementParams(
                          startDate: null,
                          endDate: null,
                          paymentType: params?.paymentType);
                  _selectedDates.value = [];
                },
              );
            },
          ),
        ],
      ),
    );
  }

  void sortTable(CreditStatementSorting? sort) {
    if (sort != null) {
      final params = ref.read(creditStatementArgProvider);
      ref.read(creditStatementArgProvider.notifier).state =
          params?.sort(sort) ?? CreditStatementParams(sortBy: sort);
    }
  }

  void _closeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}
