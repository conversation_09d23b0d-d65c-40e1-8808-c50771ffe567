import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_flutter_core/services/api/td_api.dart';
import 'package:td_procurement/app/account/data/models/account_statement.dart';
import 'package:td_procurement/app/credit/data/models/credit_statement.dart';
import 'package:td_procurement/app/credit/data/models/loan_repayment.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/config/app_config/app_config.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';

class CreditDataSourceImplementation extends CreditDataSource {
  final TdApiClient _apiClient;
  final AppConfig _config;
  CreditDataSourceImplementation(this._apiClient, this._config);

  @override
  Future<CreditStatement> fetchCreditStatement(
      CreditStatementParams params) async {
    final res = await _apiClient.get(
      "${_config.consoleUrl}$kLoansApiPath",
      queryParameters: params.toMap(),
    );

    // Get balance from balance API
    // final balanceRes = await _apiClient.get(
    //   "${_config.consoleUrl}$kLoanBalanceApiPath",
    // );

    // final loansData = res.data['data'];
    // final balanceData = balanceRes.data['data'];

    final loansData = res.data['data'];
    // final balanceData = await fetchLoanBalance();

    // Create a CreditStatement from the loans data and balance
    return CreditStatement(
      (loansData['loans'] as List<dynamic>)
          .map((e) => Loan.fromJson(e as Map<String, dynamic>))
          .toList(),
      // balanceData.balance,
      1000,
      0, // totalCredits - set to 0 as requested
      0, // totalDebits - set to 0 as requested
      (loansData['loans'] as List<dynamic>).length, // totalCount
      loansData['pagination'] != null
          ? Pagination.fromJson(loansData['pagination'] as Map<String, dynamic>)
          : Pagination(1, 10), // default pagination
      Currency('NGN', '₦'), // default currency - can be made dynamic later
    );
  }

  @override
  Future<LoanBalance> fetchLoanBalance() async {
    final res = await _apiClient.get(
      "${_config.consoleUrl}$kLoanBalanceApiPath",
    );
    return LoanBalance.fromJson(res.data['data']);
  }

  @override
  Future<bool> sendCreditStatement(CreditStatementParams params) async {
    await _apiClient.get(
      "${_config.awsApiUrlV3}$kLoansApiPath",
      queryParameters: params.toMap(),
    );
    return true;
  }
}

abstract class CreditDataSource {
  Future<CreditStatement> fetchCreditStatement(CreditStatementParams params);
  Future<LoanBalance> fetchLoanBalance();
  Future<bool> sendCreditStatement(CreditStatementParams params);
}

final creditDataProvider = Provider<CreditDataSource>((ref) {
  final apiClient = ref.read(apiClientProvider);
  final config = ref.read(appConfigProvider);
  return CreditDataSourceImplementation(apiClient, config);
});
