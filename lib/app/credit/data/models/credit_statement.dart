import 'package:json_annotation/json_annotation.dart';
import 'package:td_procurement/app/credit/data/models/loan_repayment.dart';
import 'package:td_procurement/app/account/data/models/account_statement.dart';

part 'credit_statement.g.dart';

@JsonSerializable()
class CreditStatement {
  final List<Loan> loans;
  final num balance;
  final num totalCredits;
  final num totalDebits;
  final num totalCount;
  final Pagination pagination;
  final Currency currency;

  factory CreditStatement.fromJson(Map<String, Object?> json) =>
      _$CreditStatementFromJson(json);

  CreditStatement(
    this.loans,
    this.balance,
    this.totalCredits,
    this.totalDebits,
    this.totalCount,
    this.pagination,
    this.currency,
  );

  Map<String, Object?> toJson() => _$CreditStatementToJson(this);

  // Convert loans to a flat list of repayments for table display
  List<LoanRepayment> get allRepayments {
    return loans.expand((loan) => loan.repayments).toList();
  }
}
