import 'package:json_annotation/json_annotation.dart';

part 'loan_repayment.g.dart';

@JsonSerializable()
class LoanRepayment {
  @JsonKey(name: '_id')
  final String id;
  final String retailOutletId;
  final String loanId;
  final num amount;
  final String status;
  final DateTime dueAt;
  final DateTime date;
  final num payment;
  final num outstandingPrincipal;
  final num interest;
  final num principal;
  final num earlyRepayment;
  final DateTime createdAt;

  factory LoanRepayment.fromJson(Map<String, Object?> json) =>
      _$LoanRepaymentFromJson(json);

  LoanRepayment(
    this.id,
    this.retailOutletId,
    this.loanId,
    this.amount,
    this.status,
    this.dueAt,
    this.date,
    this.payment,
    this.outstandingPrincipal,
    this.interest,
    this.principal,
    this.earlyRepayment,
    this.createdAt,
  );

  Map<String, Object?> toJson() => _$LoanRepaymentToJson(this);
}

@JsonSerializable()
class Loan {
  @Json<PERSON>ey(name: '_id')
  final String id;
  final String loanId;
  final String retailOutletId;
  final String createdBy;
  final num principal;
  final num balance;
  final String payChannel;
  final String serviceChannel;
  final String paymentMethod;
  final DateTime createdAt;
  final dynamic payeeDetails;
  final List<LoanRepayment> repayments;

  factory Loan.fromJson(Map<String, Object?> json) => _$LoanFromJson(json);

  Loan(
    this.id,
    this.loanId,
    this.retailOutletId,
    this.createdBy,
    this.principal,
    this.balance,
    this.payChannel,
    this.serviceChannel,
    this.paymentMethod,
    this.createdAt,
    this.payeeDetails,
    this.repayments,
  );

  Map<String, Object?> toJson() => _$LoanToJson(this);

  factory Loan.defaultValue() {
    return Loan(
      'id',
      'loanId',
      'retailOutletId',
      'createdBy',
      0,
      0,
      'payChannel',
      'serviceChannel',
      'paymentMethod',
      DateTime.now(),
      null,
      [],
    );
  }
}

@JsonSerializable()
class LoanBalance {
  final num balance;

  factory LoanBalance.fromJson(Map<String, Object?> json) =>
      _$LoanBalanceFromJson(json);

  LoanBalance(this.balance);

  Map<String, Object?> toJson() => _$LoanBalanceToJson(this);
}
