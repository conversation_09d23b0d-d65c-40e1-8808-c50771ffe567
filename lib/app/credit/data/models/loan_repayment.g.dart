// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_repayment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoanRepayment _$LoanRepaymentFromJson(Map<String, dynamic> json) =>
    LoanRepayment(
      json['_id'] as String,
      json['retailOutletId'] as String,
      json['loanId'] as String,
      json['amount'] as num,
      json['status'] as String,
      DateTime.parse(json['dueAt'] as String),
      DateTime.parse(json['date'] as String),
      json['payment'] as num,
      json['outstandingPrincipal'] as num,
      json['interest'] as num,
      json['principal'] as num,
      json['earlyRepayment'] as num,
      DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$LoanRepaymentToJson(LoanRepayment instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'retailOutletId': instance.retailOutletId,
      'loanId': instance.loanId,
      'amount': instance.amount,
      'status': instance.status,
      'dueAt': instance.dueAt.toIso8601String(),
      'date': instance.date.toIso8601String(),
      'payment': instance.payment,
      'outstandingPrincipal': instance.outstandingPrincipal,
      'interest': instance.interest,
      'principal': instance.principal,
      'earlyRepayment': instance.earlyRepayment,
      'createdAt': instance.createdAt.toIso8601String(),
    };

Loan _$LoanFromJson(Map<String, dynamic> json) => Loan(
      json['_id'] as String,
      json['loanId'] as String,
      json['retailOutletId'] as String,
      json['createdBy'] as String,
      json['principal'] as num,
      json['balance'] as num,
      json['payChannel'] as String,
      json['serviceChannel'] as String,
      json['paymentMethod'] as String,
      DateTime.parse(json['createdAt'] as String),
      json['payeeDetails'],
      (json['repayments'] as List<dynamic>)
          .map((e) => LoanRepayment.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$LoanToJson(Loan instance) => <String, dynamic>{
      '_id': instance.id,
      'loanId': instance.loanId,
      'retailOutletId': instance.retailOutletId,
      'createdBy': instance.createdBy,
      'principal': instance.principal,
      'balance': instance.balance,
      'payChannel': instance.payChannel,
      'serviceChannel': instance.serviceChannel,
      'paymentMethod': instance.paymentMethod,
      'createdAt': instance.createdAt.toIso8601String(),
      'payeeDetails': instance.payeeDetails,
      'repayments': instance.repayments,
    };

LoanBalance _$LoanBalanceFromJson(Map<String, dynamic> json) => LoanBalance(
      json['balance'] as num,
    );

Map<String, dynamic> _$LoanBalanceToJson(LoanBalance instance) =>
    <String, dynamic>{
      'balance': instance.balance,
    };
