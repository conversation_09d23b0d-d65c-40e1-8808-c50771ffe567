import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/credit/data/models/credit_statement.dart';
import 'package:td_procurement/app/credit/data/models/loan_repayment.dart';
import 'package:td_procurement/app/credit/data/sources/credit_data_source.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';
import 'package:td_procurement/core/helpers/dio_interceptor.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

class CreditRepoImplementation extends CreditRepo {
  final Ref _ref;

  CreditRepoImplementation(this._ref);

  late final CreditDataSource _dataSource = _ref.read(creditDataProvider);

  @override
  Future<ApiResponse<CreditStatement>> fetchCreditStatement(
      CreditStatementParams params) {
    return dioInterceptor(
        () => _dataSource.fetchCreditStatement(params), _ref);
  }

  @override
  Future<ApiResponse<LoanBalance>> fetchLoanBalance() {
    return dioInterceptor(() => _dataSource.fetchLoanBalance(), _ref);
  }

  @override
  Future<ApiResponse<bool>> sendCreditStatement(CreditStatementParams params) {
    return dioInterceptor(() => _dataSource.sendCreditStatement(params), _ref);
  }
}

abstract class CreditRepo {
  Future<ApiResponse<CreditStatement>> fetchCreditStatement(
      CreditStatementParams params);
  Future<ApiResponse<LoanBalance>> fetchLoanBalance();
  Future<ApiResponse<bool>> sendCreditStatement(CreditStatementParams params);
}

final creditRepoProvider = Provider<CreditRepo>((ref) {
  return CreditRepoImplementation(ref);
});
